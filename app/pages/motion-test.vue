<script setup lang="ts">
import { useBrutalMotion } from '~/composables/useBrutalMotion';
import { useWeb3Animations } from '~/composables/useWeb3Animations';
import { usePageTransitions } from '~/composables/usePageTransitions';

// SEO and meta
useHead({
  title: 'Motion Testing - DEFI.AI',
  meta: [
    {
      name: 'description',
      content: 'Testing page for Neo-Brutalism motion patterns and animations.',
    },
  ],
});

// Motion composables
const { 
  brutalSlideLeft, 
  brutalSlideRight, 
  brutalPop, 
  brutalFade,
  brutalBounce,
  brutalHover,
  brutalTouch,
  prefersReducedMotion 
} = useBrutalMotion();

const {
  walletButtonAnimation,
  transactionAnimation,
  portfolioValueAnimation,
  gasFeeAnimation,
  protocolAnimation,
  yieldAnimation,
  notificationAnimation,
  setWalletConnecting,
  setWalletConnected,
  setWalletError,
  resetState
} = useWeb3Animations();

const {
  createLoadingAnimation,
  createErrorAnimation,
  createSuccessAnimation
} = usePageTransitions();

// Test state
const testState = ref({
  showSlideLeft: false,
  showSlideRight: false,
  showPop: false,
  showFade: false,
  showBounce: false,
  showStagger: false,
  showWeb3Demo: false,
  showNotification: false,
  currentTest: 'none'
});

// Test functions
const runSlideTest = () => {
  testState.value.currentTest = 'slide';
  testState.value.showSlideLeft = true;
  
  setTimeout(() => {
    testState.value.showSlideRight = true;
  }, 500);
  
  setTimeout(() => {
    testState.value.showSlideLeft = false;
    testState.value.showSlideRight = false;
    testState.value.currentTest = 'none';
  }, 3000);
};

const runPopTest = () => {
  testState.value.currentTest = 'pop';
  testState.value.showPop = true;
  
  setTimeout(() => {
    testState.value.showPop = false;
    testState.value.currentTest = 'none';
  }, 2000);
};

const runFadeTest = () => {
  testState.value.currentTest = 'fade';
  testState.value.showFade = true;
  
  setTimeout(() => {
    testState.value.showFade = false;
    testState.value.currentTest = 'none';
  }, 2000);
};

const runBounceTest = () => {
  testState.value.currentTest = 'bounce';
  testState.value.showBounce = true;
  
  setTimeout(() => {
    testState.value.showBounce = false;
    testState.value.currentTest = 'none';
  }, 2000);
};

const runStaggerTest = () => {
  testState.value.currentTest = 'stagger';
  testState.value.showStagger = true;
  
  setTimeout(() => {
    testState.value.showStagger = false;
    testState.value.currentTest = 'none';
  }, 3000);
};

const runWeb3Demo = () => {
  testState.value.currentTest = 'web3';
  testState.value.showWeb3Demo = true;
  
  // Simulate wallet connection flow
  setWalletConnecting();
  
  setTimeout(() => {
    setWalletConnected();
  }, 2000);
  
  setTimeout(() => {
    resetState();
    testState.value.showWeb3Demo = false;
    testState.value.currentTest = 'none';
  }, 4000);
};

const showNotificationTest = () => {
  testState.value.currentTest = 'notification';
  testState.value.showNotification = true;
  
  setTimeout(() => {
    testState.value.showNotification = false;
    testState.value.currentTest = 'none';
  }, 3000);
};

const resetAllTests = () => {
  Object.keys(testState.value).forEach(key => {
    if (key.startsWith('show')) {
      testState.value[key] = false;
    }
  });
  testState.value.currentTest = 'none';
  resetState();
};

// Performance monitoring
const performanceMetrics = ref({
  animationCount: 0,
  averageDuration: 0,
  lastFrameTime: 0
});

const monitorPerformance = () => {
  const startTime = performance.now();
  
  requestAnimationFrame(() => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    performanceMetrics.value.animationCount++;
    performanceMetrics.value.averageDuration = 
      (performanceMetrics.value.averageDuration + duration) / 2;
    performanceMetrics.value.lastFrameTime = duration;
  });
};

// Watch for test changes to monitor performance
watch(() => testState.value.currentTest, () => {
  if (testState.value.currentTest !== 'none') {
    monitorPerformance();
  }
});
</script>

<template>
  <div class="min-h-screen bg-brutal-charcoal">
    <!-- Header -->
    <div class="border-brutal-heavy bg-brutal-black container-spacing section-spacing">
      <div class="mx-auto max-w-7xl">
        <div class="mobile-margin-x">
          <h1 class="font-brutal text-3xl sm:text-4xl md:text-5xl lg:text-6xl text-brutal-white">
            MOTION
            <span class="text-electric-blue">TESTING</span>
          </h1>
          <p class="font-mono-brutal mt-brutal mobile-text text-brutal-white">
            > NEO-BRUTALISM ANIMATION PATTERNS
          </p>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-7xl container-spacing section-spacing">
      <!-- Test Controls -->
      <div class="mb-brutal-2xl">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">ANIMATION TESTS</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="mobile-grid gap-4">
              <Button
                @click="runSlideTest"
                class="border-brutal bg-electric-blue px-4 py-3 font-brutal text-sm text-brutal-white shadow-brutal hover-brutal-neon mobile-tap"
                :disabled="testState.currentTest !== 'none'"
              >
                SLIDE TEST
              </Button>
              <Button
                @click="runPopTest"
                class="border-brutal bg-hot-magenta px-4 py-3 font-brutal text-sm text-brutal-white shadow-brutal hover-brutal-electric mobile-tap"
                :disabled="testState.currentTest !== 'none'"
              >
                POP TEST
              </Button>
              <Button
                @click="runFadeTest"
                class="border-brutal bg-acid-green px-4 py-3 font-brutal text-sm text-brutal-black shadow-brutal hover-brutal-neon mobile-tap"
                :disabled="testState.currentTest !== 'none'"
              >
                FADE TEST
              </Button>
              <Button
                @click="runBounceTest"
                class="border-brutal bg-plasma-orange px-4 py-3 font-brutal text-sm text-brutal-black shadow-brutal hover-brutal-electric mobile-tap"
                :disabled="testState.currentTest !== 'none'"
              >
                BOUNCE TEST
              </Button>
              <Button
                @click="runStaggerTest"
                class="border-brutal bg-cyber-purple px-4 py-3 font-brutal text-sm text-brutal-white shadow-brutal hover-brutal-magenta mobile-tap"
                :disabled="testState.currentTest !== 'none'"
              >
                STAGGER TEST
              </Button>
              <Button
                @click="runWeb3Demo"
                class="border-brutal bg-neon-lime px-4 py-3 font-brutal text-sm text-brutal-black shadow-brutal hover-brutal-cyan mobile-tap"
                :disabled="testState.currentTest !== 'none'"
              >
                WEB3 DEMO
              </Button>
              <Button
                @click="showNotificationTest"
                class="border-brutal bg-laser-red px-4 py-3 font-brutal text-sm text-brutal-white shadow-brutal hover-brutal-pink mobile-tap"
                :disabled="testState.currentTest !== 'none'"
              >
                NOTIFICATION
              </Button>
              <Button
                @click="resetAllTests"
                class="border-brutal bg-brutal-black px-4 py-3 font-brutal text-sm text-brutal-white shadow-brutal hover-brutal-electric mobile-tap"
              >
                RESET ALL
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Test Area -->
      <div class="mb-brutal-2xl min-h-[400px] border-brutal bg-brutal-white p-brutal-xl">
        <div class="text-center">
          <h2 class="font-brutal text-2xl text-brutal-black mb-brutal">
            TEST AREA: {{ testState.currentTest.toUpperCase() }}
          </h2>
          
          <!-- Slide Test Elements -->
          <div v-if="testState.showSlideLeft" 
               class="mb-4 border-brutal bg-electric-blue p-4 shadow-brutal"
               v-motion="brutalSlideLeft">
            <div class="font-brutal text-brutal-white">SLIDE LEFT ANIMATION</div>
          </div>
          
          <div v-if="testState.showSlideRight" 
               class="mb-4 border-brutal bg-hot-magenta p-4 shadow-brutal"
               v-motion="brutalSlideRight">
            <div class="font-brutal text-brutal-white">SLIDE RIGHT ANIMATION</div>
          </div>
          
          <!-- Pop Test Element -->
          <div v-if="testState.showPop" 
               class="mb-4 border-brutal bg-acid-green p-4 shadow-brutal"
               v-motion="brutalPop">
            <div class="font-brutal text-brutal-black">POP ANIMATION</div>
          </div>
          
          <!-- Fade Test Element -->
          <div v-if="testState.showFade" 
               class="mb-4 border-brutal bg-plasma-orange p-4 shadow-brutal"
               v-motion="brutalFade">
            <div class="font-brutal text-brutal-black">FADE ANIMATION</div>
          </div>
          
          <!-- Bounce Test Element -->
          <div v-if="testState.showBounce" 
               class="mb-4 border-brutal bg-cyber-purple p-4 shadow-brutal"
               v-motion="brutalBounce">
            <div class="font-brutal text-brutal-white">BOUNCE ANIMATION</div>
          </div>
          
          <!-- Stagger Test Elements -->
          <div v-if="testState.showStagger" class="mobile-grid gap-4">
            <div v-for="i in 6" :key="i"
                 class="border-brutal bg-neon-lime p-4 shadow-brutal"
                 v-motion
                 :initial="{ opacity: 0, y: 20 }"
                 :visible="{ opacity: 1, y: 0 }"
                 :delay="i * 100"
                 :duration="250">
              <div class="font-brutal text-brutal-black">ITEM {{ i }}</div>
            </div>
          </div>
          
          <!-- Web3 Demo -->
          <div v-if="testState.showWeb3Demo" class="space-y-4">
            <Button
              class="border-brutal bg-hot-magenta px-6 py-4 font-brutal text-brutal-white shadow-brutal"
              v-motion="walletButtonAnimation">
              WALLET CONNECTION
            </Button>
            
            <div class="border-brutal bg-brutal-black p-4"
                 v-motion="transactionAnimation">
              <div class="font-mono-brutal text-neon-lime">TRANSACTION STATUS</div>
            </div>
          </div>
          
          <!-- Notification Test -->
          <div v-if="testState.showNotification"
               class="fixed top-4 right-4 border-brutal bg-acid-green p-4 shadow-brutal z-50"
               v-motion="notificationAnimation">
            <div class="font-brutal text-brutal-black">NOTIFICATION MESSAGE</div>
          </div>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="mb-brutal-2xl">
        <Card class="border-brutal bg-brutal-black shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-neon-lime">PERFORMANCE METRICS</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="mobile-grid gap-4">
              <div class="border-brutal bg-brutal-charcoal p-4 text-center">
                <div class="font-mono-brutal text-xs text-brutal-white">REDUCED MOTION</div>
                <div class="font-brutal text-2xl text-brutal-white">
                  {{ prefersReducedMotion() ? 'ON' : 'OFF' }}
                </div>
              </div>
              <div class="border-brutal bg-brutal-charcoal p-4 text-center">
                <div class="font-mono-brutal text-xs text-brutal-white">ANIMATIONS</div>
                <div class="font-brutal text-2xl text-brutal-white">{{ performanceMetrics.animationCount }}</div>
              </div>
              <div class="border-brutal bg-brutal-charcoal p-4 text-center">
                <div class="font-mono-brutal text-xs text-brutal-white">AVG DURATION</div>
                <div class="font-brutal text-2xl text-brutal-white">{{ performanceMetrics.averageDuration.toFixed(1) }}ms</div>
              </div>
              <div class="border-brutal bg-brutal-charcoal p-4 text-center">
                <div class="font-mono-brutal text-xs text-brutal-white">LAST FRAME</div>
                <div class="font-brutal text-2xl text-brutal-white">{{ performanceMetrics.lastFrameTime.toFixed(1) }}ms</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>
