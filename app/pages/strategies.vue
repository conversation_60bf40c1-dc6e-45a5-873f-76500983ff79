<script setup lang="ts">
// SEO and meta
useHead({
  title: 'Strategy Configuration - DEFI.AI',
  meta: [
    {
      name: 'description',
      content:
        'Configure your AI-powered DeFi strategy with risk tolerance, protocol preferences, and optimization settings.',
    },
  ],
});

// Strategy options
const strategies = ref([
  {
    id: 'conservative',
    name: 'CONSERVATIVE',
    description: 'Low risk, stable returns with focus on established protocols',
    expectedApy: '8-15%',
    riskLevel: 'LOW',
    color: 'neon-cyan',
    allocation: { aave: 60, lido: 35, uniswap: 5 },
    features: ['Capital preservation', 'Stable yields', 'Low volatility'],
  },
  {
    id: 'balanced',
    name: 'BALANCED',
    description: 'Moderate risk with balanced exposure across protocols',
    expectedApy: '15-25%',
    riskLevel: 'MODERATE',
    color: 'neon-lime',
    allocation: { aave: 40, lido: 30, uniswap: 30 },
    features: [
      'Balanced risk/reward',
      'Diversified exposure',
      'Moderate volatility',
    ],
  },
  {
    id: 'aggressive',
    name: 'AGGRESSIVE',
    description: 'High risk, maximum yield potential with active trading',
    expectedApy: '25-50%',
    riskLevel: 'HIGH',
    color: 'neon-orange',
    allocation: { aave: 20, lido: 20, uniswap: 60 },
    features: [
      'Maximum yield potential',
      'Active rebalancing',
      'High volatility',
    ],
  },
]);

// Selected strategy
const selectedStrategy = ref('balanced');

// Risk tolerance slider
const riskTolerance = ref(5);

// Protocol preferences
const protocolPreferences = ref({
  aave: true,
  uniswap: true,
  lido: true,
});

// Advanced settings
const advancedSettings = ref({
  autoRebalance: true,
  yieldHarvesting: true,
  gasOptimization: true,
  slippageTolerance: 0.5,
  rebalanceThreshold: 5,
});

// Deploy strategy function
const deployStrategy = () => {
  console.log('Deploying strategy:', {
    strategy: selectedStrategy.value,
    riskTolerance: riskTolerance.value,
    protocols: protocolPreferences.value,
    settings: advancedSettings.value,
  });
  // TODO: Implement actual strategy deployment
};
</script>

<template>
  <div class="min-h-screen bg-brutal-charcoal">
    <!-- Header -->
    <div class="border-brutal-heavy bg-brutal-black container-spacing section-spacing">
      <div class="mx-auto max-w-7xl">
        <div class="mobile-margin-x">
          <h1 class="font-brutal text-4xl text-brutal-white md:text-6xl">
            STRATEGY
            <span class="text-neon-lime">CONFIGURATION</span>
          </h1>
          <p class="font-mono-brutal mt-brutal text-brutal-white">
            > CONFIGURE YOUR AI-POWERED DEFI STRATEGY
          </p>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-7xl container-spacing section-spacing">
      <!-- Strategy Selection -->
      <div class="mb-brutal-2xl">
        <h2 class="font-brutal mb-6 text-2xl text-brutal-white">SELECT STRATEGY</h2>
        <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div
            v-for="strategy in strategies"
            :key="strategy.id"
            class="cursor-pointer border-brutal bg-brutal-white p-6 shadow-brutal transition-all hover-brutal brutal-override"
            :class="{
              'shadow-brutal-neon-lime': selectedStrategy === strategy.id
            }"
            @click="selectedStrategy = strategy.id"
          >
            <div class="mb-4 flex items-center justify-between">
              <h3 class="font-brutal text-xl text-brutal-black">{{ strategy.name }}</h3>
              <div
                class="h-4 w-4 border-brutal brutal-override"
                :class="'bg-' + strategy.color"
              ></div>
            </div>

            <p class="font-mono-brutal mb-4 text-sm text-brutal-black">
              {{ strategy.description }}
            </p>

            <div class="mb-4 space-y-2">
              <div class="flex justify-between">
                <span class="font-mono-brutal text-xs text-brutal-black">EXPECTED APY:</span>
                <span class="font-brutal text-xs" :class="'text-' + strategy.color">{{ strategy.expectedApy }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-mono-brutal text-xs text-brutal-black">RISK LEVEL:</span>
                <span class="font-brutal text-xs text-brutal-black">{{ strategy.riskLevel }}</span>
              </div>
            </div>

            <!-- Allocation Preview -->
            <div class="mb-4">
              <div class="font-mono-brutal mb-2 text-xs text-brutal-black">ALLOCATION:</div>
              <div class="space-y-1">
                <div class="flex justify-between">
                  <span class="font-mono-brutal text-xs text-brutal-black">AAVE:</span>
                  <span class="font-mono-brutal text-xs text-brutal-black">{{ strategy.allocation.aave }}%</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono-brutal text-xs text-brutal-black">LIDO:</span>
                  <span class="font-mono-brutal text-xs text-brutal-black">{{ strategy.allocation.lido }}%</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-mono-brutal text-xs text-brutal-black">UNISWAP:</span>
                  <span class="font-mono-brutal text-xs text-brutal-black">{{ strategy.allocation.uniswap }}%</span>
                </div>
              </div>
            </div>

            <!-- Features -->
            <div>
              <div class="font-mono-brutal mb-2 text-xs text-brutal-black">FEATURES:</div>
              <ul class="space-y-1">
                <li
                  v-for="feature in strategy.features"
                  :key="feature"
                  class="font-mono-brutal text-xs text-brutal-black"
                >
                  • {{ feature }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Risk Tolerance -->
      <div class="mb-brutal-2xl">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">RISK TOLERANCE</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-brutal">
              <div class="flex items-center justify-between">
                <span class="font-mono-brutal text-sm text-brutal-black">CONSERVATIVE</span>
                <span class="font-mono-brutal text-sm text-brutal-black">AGGRESSIVE</span>
              </div>
              <div class="relative">
                <input
                  v-model="riskTolerance"
                  type="range"
                  min="1"
                  max="10"
                  class="range-brutal w-full"
                />
                <div class="mt-2 text-center">
                  <span class="font-brutal text-2xl text-brutal-black">{{ riskTolerance }}/10</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Protocol Selection -->
      <div class="mb-brutal-2xl">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">PROTOCOL SELECTION</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 gap-brutal md:grid-cols-3">
              <div
                v-for="(enabled, protocol) in protocolPreferences"
                :key="protocol"
                class="border-brutal bg-brutal-charcoal p-brutal brutal-override"
              >
                <div class="flex items-center justify-between">
                  <span class="font-brutal text-brutal-white">{{ protocol.toUpperCase() }}</span>
                  <input
                    v-model="protocolPreferences[protocol]"
                    type="checkbox"
                    class="h-6 w-6 checkbox-brutal"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Advanced Settings -->
      <div class="mb-brutal-2xl">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">ADVANCED SETTINGS</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-brutal-lg">
              <!-- Toggle Settings -->
              <div class="grid grid-cols-1 gap-brutal md:grid-cols-3">
                <div class="border-brutal bg-brutal-charcoal p-brutal brutal-override">
                  <div class="flex items-center justify-between">
                    <span class="font-mono-brutal text-sm text-brutal-white">AUTO REBALANCE</span>
                    <input
                      v-model="advancedSettings.autoRebalance"
                      type="checkbox"
                      class="h-6 w-6 checkbox-brutal"
                    />
                  </div>
                </div>
                <div class="border-brutal bg-brutal-charcoal p-brutal brutal-override">
                  <div class="flex items-center justify-between">
                    <span class="font-mono-brutal text-sm text-brutal-white">YIELD HARVESTING</span>
                    <input
                      v-model="advancedSettings.yieldHarvesting"
                      type="checkbox"
                      class="h-6 w-6 checkbox-brutal"
                    />
                  </div>
                </div>
                <div class="border-brutal bg-brutal-charcoal p-brutal brutal-override">
                  <div class="flex items-center justify-between">
                    <span class="font-mono-brutal text-sm text-brutal-white">GAS OPTIMIZATION</span>
                    <input
                      v-model="advancedSettings.gasOptimization"
                      type="checkbox"
                      class="h-6 w-6 checkbox-brutal"
                    />
                  </div>
                </div>
              </div>

              <!-- Numeric Settings -->
              <div class="grid grid-cols-1 gap-brutal-lg md:grid-cols-2">
                <div>
                  <label class="font-mono-brutal mb-2 block text-sm text-brutal-black">
                    SLIPPAGE TOLERANCE (%)
                  </label>
                  <input
                    v-model="advancedSettings.slippageTolerance"
                    type="number"
                    step="0.1"
                    min="0.1"
                    max="5"
                    class="input-brutal w-full bg-brutal-charcoal p-brutal font-mono-brutal text-brutal-white"
                  />
                </div>
                <div>
                  <label class="font-mono-brutal mb-2 block text-sm text-brutal-black">
                    REBALANCE THRESHOLD (%)
                  </label>
                  <input
                    v-model="advancedSettings.rebalanceThreshold"
                    type="number"
                    min="1"
                    max="20"
                    class="input-brutal w-full bg-brutal-charcoal p-brutal font-mono-brutal text-brutal-white"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Deploy Button -->
      <div class="text-center">
        <Button 
        class="border-brutal bg-neon-lime px-brutal-xl py-brutal-lg font-brutal mobile-text text-brutal-black shadow-brutal hover-brutal-electric mobile-tap"
         @click="deployStrategy"
        >
          DEPLOY STRATEGY
        </Button>
      </div>
    </div>
  </div>
</template>
