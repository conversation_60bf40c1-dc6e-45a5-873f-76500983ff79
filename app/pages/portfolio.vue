<script setup lang="ts">
import { useBrutalMotion } from '~/composables/useBrutalMotion';
import { useWeb3Animations } from '~/composables/useWeb3Animations';

// SEO and meta
useHead({
  title: 'Portfolio Dashboard - DEFI.AI',
  meta: [
    {
      name: 'description',
      content:
        'AI-powered portfolio dashboard with real-time analytics, optimization recommendations, and performance metrics.',
    },
  ],
});

// Motion setup
const {
  brutalSlideLeft,
  brutalSlideRight,
  brutalPop,
  brutalFade,
  prefersReducedMotion,
} = useBrutalMotion();

const { portfolioValueAnimation, transactionAnimation } = useWeb3Animations();

// Portfolio data (mock)
const portfolioData = ref({
  totalValue: '$2,456,789',
  totalValueChange: '+12.3%',
  apy: '24.7%',
  apyChange: '+2.1%',
  riskScore: '6.2/10',
  riskLevel: 'MODERATE',
  gasOptimization: '87%',
  gasOptimizationChange: 'EFFICIENT',
});

// Asset allocation data
const assetAllocation = ref([
  {
    protocol: 'AAVE',
    percentage: 45,
    value: '$1,105,555',
    apy: '18.5%',
    color: 'neon-lime',
  },
  {
    protocol: 'UNISWAP V3',
    percentage: 35,
    value: '$859,876',
    apy: '32.1%',
    color: 'neon-cyan',
  },
  {
    protocol: 'LIDO',
    percentage: 20,
    value: '$491,358',
    apy: '24.3%',
    color: 'neon-orange',
  },
]);

// Recent transactions
const recentTransactions = ref([
  {
    type: 'DEPOSIT',
    protocol: 'AAVE',
    amount: '+$50,000',
    time: '2 hours ago',
    status: 'CONFIRMED',
  },
  {
    type: 'REBALANCE',
    protocol: 'UNISWAP V3',
    amount: '+$25,000',
    time: '6 hours ago',
    status: 'CONFIRMED',
  },
  {
    type: 'HARVEST',
    protocol: 'LIDO',
    amount: '+$1,234',
    time: '12 hours ago',
    status: 'CONFIRMED',
  },
  {
    type: 'WITHDRAW',
    protocol: 'AAVE',
    amount: '-$10,000',
    time: '1 day ago',
    status: 'CONFIRMED',
  },
]);

// AI recommendations
const aiRecommendations = ref([
  {
    title: 'REBALANCE OPPORTUNITY',
    description: 'Move 15% from AAVE to LIDO for +2.1% APY increase',
    impact: '+$2,456/month',
    confidence: 92,
    urgency: 'HIGH',
  },
  {
    title: 'YIELD HARVEST',
    description: 'Claim pending rewards across all protocols',
    impact: '+$1,234',
    confidence: 98,
    urgency: 'MEDIUM',
  },
  {
    title: 'GAS OPTIMIZATION',
    description: 'Batch transactions during low gas period',
    impact: 'Save $45',
    confidence: 85,
    urgency: 'LOW',
  },
]);

// Performance metrics
const performanceMetrics = ref([
  { label: '24H CHANGE', value: '+$12,456', percentage: '+0.51%' },
  { label: '7D CHANGE', value: '+$89,123', percentage: '*****%' },
  { label: '30D CHANGE', value: '+$234,567', percentage: '+10.55%' },
  { label: 'ALL TIME', value: '+$456,789', percentage: '+22.84%' },
]);
</script>

<template>
  <div class="min-h-screen bg-brutal-charcoal">
    <!-- Header -->
    <div class="border-brutal-heavy bg-brutal-black container-spacing section-spacing">
      <div class="mx-auto max-w-7xl">
        <div
          class="mobile-margin-x"
          v-motion
          :initial="{ opacity: 0, y: 30 }"
          :enter="{ opacity: 1, y: 0 }"
          :duration="400"
        >
          <h1 class="font-brutal text-3xl sm:text-4xl md:text-5xl lg:text-6xl text-brutal-white">
            PORTFOLIO
            <span class="text-electric-blue">DASHBOARD</span>
          </h1>
          <p class="font-mono-brutal mt-brutal mobile-text text-brutal-white">
            > AI-POWERED PORTFOLIO MANAGEMENT SYSTEM
          </p>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-7xl container-spacing section-spacing">
      <!-- Portfolio Overview -->
      <div class="mb-brutal-2xl mobile-grid gap-brutal-lg">
        <!-- Total Value -->
        <Card
          class="border-brutal bg-brutal-white shadow-brutal-electric-blue hover-brutal-electric mobile-tap"
          v-motion
          :initial="{ opacity: 0, scale: 0.9 }"
          :visible="{ opacity: 1, scale: 1 }"
          :delay="200"
          :duration="300"
        >
          <CardHeader>
            <CardTitle class="font-brutal mobile-text text-brutal-black">TOTAL VALUE</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="font-brutal text-3xl sm:text-4xl text-brutal-black">{{ portfolioData.totalValue }}</div>
            <div class="font-mono-brutal text-xs sm:text-sm text-electric-blue">{{ portfolioData.totalValueChange }}</div>
          </CardContent>
        </Card>

        <!-- APY -->
        <Card
          class="border-brutal bg-brutal-white shadow-brutal-acid-green hover-brutal-neon mobile-tap"
          v-motion
          :initial="{ opacity: 0, scale: 0.9 }"
          :visible="{ opacity: 1, scale: 1 }"
          :delay="300"
          :duration="300"
        >
          <CardHeader>
            <CardTitle class="font-brutal mobile-text text-brutal-black">CURRENT APY</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="font-brutal text-3xl sm:text-4xl text-brutal-black">{{ portfolioData.apy }}</div>
            <div class="font-mono-brutal text-xs sm:text-sm text-acid-green">{{ portfolioData.apyChange }}</div>
          </CardContent>
        </Card>

        <!-- Risk Score -->
        <Card
          class="border-brutal bg-brutal-white shadow-brutal-hot-magenta hover-brutal-magenta mobile-tap"
          v-motion
          :initial="{ opacity: 0, scale: 0.9 }"
          :visible="{ opacity: 1, scale: 1 }"
          :delay="400"
          :duration="300"
        >
          <CardHeader>
            <CardTitle class="font-brutal mobile-text text-brutal-black">RISK SCORE</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="font-brutal text-3xl sm:text-4xl text-brutal-black">{{ portfolioData.riskScore }}</div>
            <div class="font-mono-brutal text-xs sm:text-sm text-hot-magenta">{{ portfolioData.riskLevel }}</div>
          </CardContent>
        </Card>

        <!-- Gas Optimization -->
        <Card
          class="border-brutal bg-brutal-white shadow-brutal-cyber-purple hover-brutal-electric mobile-tap"
          v-motion
          :initial="{ opacity: 0, scale: 0.9 }"
          :visible="{ opacity: 1, scale: 1 }"
          :delay="500"
          :duration="300"
        >
          <CardHeader>
            <CardTitle class="font-brutal mobile-text text-brutal-black">GAS EFFICIENCY</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="font-brutal text-3xl sm:text-4xl text-brutal-black">{{ portfolioData.gasOptimization }}</div>
            <div class="font-mono-brutal text-xs sm:text-sm text-cyber-purple">{{ portfolioData.gasOptimizationChange }}</div>
          </CardContent>
        </Card>
      </div>

      <!-- Asset Allocation -->
      <div class="mb-brutal-2xl">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">ASSET ALLOCATION</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-brutal-lg">
              <div
                v-for="asset in assetAllocation"
                :key="asset.protocol"
                class="border-brutal bg-brutal-charcoal p-brutal-lg"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div
                      class="h-4 w-4 border-brutal"
                      :class="'bg-' + asset.color"
                    ></div>
                    <div>
                      <div class="font-brutal text-brutal-white">{{ asset.protocol }}</div>
                      <div class="font-mono-brutal text-sm text-brutal-white">{{ asset.value }}</div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-brutal text-brutal-white">{{ asset.percentage }}%</div>
                    <div class="font-mono-brutal text-sm" :class="'text-' + asset.color">{{ asset.apy }}</div>
                  </div>
                </div>
                <!-- Progress bar -->
                <div class="mt-4 h-2 border-brutal bg-brutal-white">
                  <div
                    class="h-full"
                    :class="'bg-' + asset.color"
                    :style="{ width: asset.percentage + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- AI Recommendations & Recent Transactions -->
      <div class="mb-8 grid grid-cols-1 gap-8 lg:grid-cols-2">
        <!-- AI Recommendations -->
        <Card class="border-brutal bg-brutal-black shadow-brutal-neon-lime">
          <CardHeader>
            <CardTitle class="font-brutal text-neon-lime">AI RECOMMENDATIONS</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="rec in aiRecommendations"
                :key="rec.title"
                class="border-brutal bg-brutal-charcoal p-4"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="font-brutal text-sm text-brutal-white">{{ rec.title }}</div>
                    <div class="font-mono-brutal mt-1 text-xs text-brutal-white">{{ rec.description }}</div>
                    <div class="font-mono-brutal mt-2 text-xs text-neon-lime">{{ rec.impact }}</div>
                  </div>
                  <div class="text-right">
                    <div
                      class="font-mono-brutal text-xs"
                      :class="{
                        'text-neon-pink': rec.urgency === 'HIGH',
                        'text-neon-orange': rec.urgency === 'MEDIUM',
                        'text-neon-cyan': rec.urgency === 'LOW'
                      }"
                    >
                      {{ rec.urgency }}
                    </div>
                    <div class="font-mono-brutal text-xs text-brutal-white">{{ rec.confidence }}%</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Recent Transactions -->
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">RECENT TRANSACTIONS</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="tx in recentTransactions"
                :key="tx.time"
                class="border-brutal bg-brutal-charcoal p-4"
              >
                <div class="flex items-center justify-between">
                  <div>
                    <div class="font-brutal text-sm text-brutal-white">{{ tx.type }}</div>
                    <div class="font-mono-brutal text-xs text-brutal-white">{{ tx.protocol }}</div>
                  </div>
                  <div class="text-right">
                    <div
                      class="font-brutal text-sm"
                      :class="{
                        'text-neon-lime': tx.amount.startsWith('+'),
                        'text-neon-pink': tx.amount.startsWith('-')
                      }"
                    >
                      {{ tx.amount }}
                    </div>
                    <div class="font-mono-brutal text-xs text-brutal-white">{{ tx.time }}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Performance Metrics -->
      <div class="mb-8">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">PERFORMANCE METRICS</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-2 gap-6 md:grid-cols-4">
              <div
                v-for="metric in performanceMetrics"
                :key="metric.label"
                class="border-brutal bg-brutal-charcoal p-4 text-center"
              >
                <div class="font-mono-brutal text-xs text-brutal-white">{{ metric.label }}</div>
                <div class="font-brutal text-2xl text-brutal-white">{{ metric.value }}</div>
                <div class="font-mono-brutal text-xs text-neon-lime">{{ metric.percentage }}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Action Buttons -->
      <div class="mobile-stack gap-3 sm:gap-4">
        <Button class="border-brutal bg-acid-green px-4 py-3 sm:px-6 font-brutal mobile-text text-brutal-black shadow-brutal hover-brutal-electric mobile-tap touch-target">
          <span class="hidden sm:inline">EXECUTE AI </span>RECOMMENDATIONS
        </Button>
        <Button class="border-brutal bg-electric-blue px-4 py-3 sm:px-6 font-brutal mobile-text text-brutal-white shadow-brutal hover-brutal-cyan mobile-tap touch-target">
          <span class="hidden sm:inline">MANUAL </span>REBALANCE
        </Button>
        <Button class="border-brutal bg-plasma-orange px-4 py-3 sm:px-6 font-brutal mobile-text text-brutal-black shadow-brutal hover-brutal-neon mobile-tap touch-target">
          <span class="hidden sm:inline">HARVEST </span>YIELDS
        </Button>
        <Button class="border-brutal bg-laser-red px-4 py-3 sm:px-6 font-brutal mobile-text text-brutal-white shadow-brutal hover-brutal-magenta mobile-tap touch-target">
          <span class="hidden sm:inline">EMERGENCY </span>WITHDRAW
        </Button>
      </div>
    </div>
  </div>
</template>
