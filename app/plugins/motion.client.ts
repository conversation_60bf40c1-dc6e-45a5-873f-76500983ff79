/**
 * @vueuse/motion Client Plugin
 * Initializes motion with Neo-Brutalism configurations
 */

import { MotionPlugin } from '@vueuse/motion'

export default defineNuxtPlugin((nuxtApp) => {
  // Check for reduced motion preference
  const prefersReducedMotion = () => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches
    }
    return false
  }

  // Neo-Brutalism motion presets
  const brutalPresets = {
    // Slide animations
    'brutal-slide-left': {
      initial: {
        opacity: 0,
        x: -40,
      },
      enter: {
        opacity: 1,
        x: 0,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
    },
    'brutal-slide-right': {
      initial: {
        opacity: 0,
        x: 40,
      },
      enter: {
        opacity: 1,
        x: 0,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
    },
    'brutal-slide-up': {
      initial: {
        opacity: 0,
        y: 20,
      },
      enter: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
    },
    'brutal-slide-down': {
      initial: {
        opacity: 0,
        y: -20,
      },
      enter: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
    },

    // Pop animations
    'brutal-pop': {
      initial: {
        opacity: 0,
        scale: 0.8,
      },
      enter: {
        opacity: 1,
        scale: 1,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
      tapped: {
        scale: 0.95,
        transition: {
          duration: 100,
          ease: 'linear',
        },
      },
    },
    'brutal-pop-large': {
      initial: {
        opacity: 0,
        scale: 0.7,
      },
      enter: {
        opacity: 1,
        scale: 1,
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
    },

    // Fade animations
    'brutal-fade': {
      initial: {
        opacity: 0,
      },
      enter: {
        opacity: 1,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
    },
    'brutal-fade-fast': {
      initial: {
        opacity: 0,
      },
      enter: {
        opacity: 1,
        transition: {
          duration: 150,
          ease: 'linear',
        },
      },
    },

    // Hover effects
    'brutal-hover': {
      initial: {
        x: 0,
        y: 0,
      },
      hovered: {
        x: -2,
        y: -2,
        transition: {
          duration: 150,
          ease: 'linear',
        },
      },
    },
    'brutal-hover-scale': {
      initial: {
        scale: 1,
      },
      hovered: {
        scale: 1.02,
        transition: {
          duration: 150,
          ease: 'linear',
        },
      },
    },

    // Stagger presets
    'brutal-stagger-fast': {
      initial: {
        opacity: 0,
        y: 20,
      },
      enter: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 200,
          ease: 'linear',
        },
      },
    },
    'brutal-stagger-medium': {
      initial: {
        opacity: 0,
        y: 20,
      },
      enter: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
    },

    // Loading animations
    'brutal-loading': {
      initial: {
        opacity: 0,
        scale: 0.8,
      },
      enter: {
        opacity: 1,
        scale: 1,
        transition: {
          duration: 200,
          ease: 'linear',
        },
      },
      loading: {
        rotate: [0, 90, 180, 270, 360],
        transition: {
          duration: 1000,
          ease: 'linear',
          repeat: Infinity,
        },
      },
    },

    // Error animations
    'brutal-error': {
      initial: {
        opacity: 0,
        x: 0,
      },
      enter: {
        opacity: 1,
        x: 0,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
      error: {
        x: [-4, 4, -4, 4, 0],
        transition: {
          duration: 400,
          ease: 'linear',
        },
      },
    },

    // Success animations
    'brutal-success': {
      initial: {
        opacity: 0,
        scale: 0.8,
      },
      enter: {
        opacity: 1,
        scale: 1,
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
      success: {
        scale: [1, 1.05, 1],
        transition: {
          duration: 400,
          ease: 'linear',
        },
      },
    },
  }

  // Create reduced motion versions
  const reducedMotionPresets = Object.keys(brutalPresets).reduce((acc, key) => {
    acc[key] = {
      initial: {},
      enter: {},
      visible: {},
      hovered: {},
      tapped: {},
      focused: {},
    }
    return acc
  }, {} as Record<string, any>)

  // Install motion plugin with brutal presets
  nuxtApp.vueApp.use(MotionPlugin, {
    directives: prefersReducedMotion() ? reducedMotionPresets : brutalPresets,
  })

  // Global motion configuration
  nuxtApp.provide('motionConfig', {
    prefersReducedMotion,
    brutalPresets,
    reducedMotionPresets,
  })

  // Performance monitoring (development only)
  if (process.dev) {
    // Monitor animation performance
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'measure' && entry.name.includes('motion')) {
          console.log(`[Motion Performance] ${entry.name}: ${entry.duration.toFixed(2)}ms`)
        }
      })
    })

    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      observer.observe({ entryTypes: ['measure'] })
    }
  }

  // Listen for reduced motion preference changes
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    
    mediaQuery.addEventListener('change', (e) => {
      console.log(`[Motion] Reduced motion preference changed: ${e.matches}`)
      // Could trigger a global state update here if needed
    })
  }
})
