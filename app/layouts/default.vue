<script setup lang="ts">
import { Button } from '~/components/ui/button';
import { Icon } from '~/components/ui/icon';
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from '~/components/ui/sheet';
import { useBrutalMotion } from '~/composables/useBrutalMotion';

// Motion setup
const { brutalSlideLeft, brutalSlideRight, brutalFade, prefersReducedMotion } =
  useBrutalMotion();

// Navigation motion
const navMotion = computed(() => {
  if (prefersReducedMotion()) return {};

  return {
    initial: { opacity: 0, y: -20 },
    enter: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 300,
        ease: 'linear',
      },
    },
  };
});

// Mobile menu state
const isMobileMenuOpen = ref(false);

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false;
};

const connectWallet = () => {
  // TODO: Implement actual wallet connection
  console.log('Wallet connected');
};
</script>

<template>
  <div class="min-h-screen bg-brutal-white">
    <!-- Navigation Header -->
    <header
      class="mobile-nav border-brutal-heavy bg-brutal-white"
      v-motion="navMotion"
    >
      <nav class="mx-auto max-w-7xl container-spacing">
        <div class="flex h-16 items-center justify-between">
          <!-- Logo -->
          <div class="flex items-center">
            <NuxtLink
              to="/"
              class="font-brutal text-xl sm:text-2xl text-brutal-black hover-brutal-electric"
              v-motion
              :initial="{ opacity: 0, x: -20 }"
              :enter="{ opacity: 1, x: 0 }"
              :delay="100"
              :duration="250"
            >
              DEFI.AI
            </NuxtLink>
          </div>

          <!-- Desktop Navigation -->
          <div class="hidden lg:block">
            <div class="ml-10 flex items-baseline space-x-2 xl:space-x-4">
              <NuxtLink
                to="/portfolio"
                class="border-brutal bg-brutal-white px-3 py-2 xl:px-4 font-brutal text-xs xl:text-sm uppercase text-brutal-black hover-brutal-neon touch-target"
                v-motion
                :initial="{ opacity: 0, y: -10 }"
                :enter="{ opacity: 1, y: 0 }"
                :delay="200"
                :duration="200"
              >
                PORTFOLIO
              </NuxtLink>
              <NuxtLink
                to="/strategies"
                class="border-brutal bg-brutal-white px-3 py-2 xl:px-4 font-brutal text-xs xl:text-sm uppercase text-brutal-black hover-brutal-cyan touch-target"
                v-motion
                :initial="{ opacity: 0, y: -10 }"
                :enter="{ opacity: 1, y: 0 }"
                :delay="250"
                :duration="200"
              >
                STRATEGIES
              </NuxtLink>
              <NuxtLink
                to="/analytics"
                class="border-brutal bg-brutal-white px-3 py-2 xl:px-4 font-brutal text-xs xl:text-sm uppercase text-brutal-black hover-brutal-magenta touch-target"
                v-motion
                :initial="{ opacity: 0, y: -10 }"
                :enter="{ opacity: 1, y: 0 }"
                :delay="300"
                :duration="200"
              >
                ANALYTICS
              </NuxtLink>
              <NuxtLink
                to="/history"
                class="border-brutal bg-brutal-white px-3 py-2 xl:px-4 font-brutal text-xs xl:text-sm uppercase text-brutal-black hover-brutal-pink touch-target"
                v-motion
                :initial="{ opacity: 0, y: -10 }"
                :enter="{ opacity: 1, y: 0 }"
                :delay="350"
                :duration="200"
              >
                HISTORY
              </NuxtLink>
            </div>
          </div>

          <!-- Wallet Connection Button -->
          <div class="hidden sm:flex items-center">
            <Button
              class="border-brutal-thick bg-hot-magenta px-4 py-2 sm:px-6 sm:py-3 font-brutal text-xs sm:text-sm uppercase text-brutal-white shadow-brutal hover-brutal-electric mobile-tap"
              @click="connectWallet"
              v-motion
              :initial="{ opacity: 0, x: 20 }"
              :enter="{ opacity: 1, x: 0 }"
              :delay="400"
              :duration="250"
            >
              <span class="hidden sm:inline">CONNECT </span>WALLET
            </Button>
          </div>

          <!-- Mobile menu button -->
          <div class="lg:hidden">
            <Sheet>
              <SheetTrigger as-child>
                <Button
                  class="border-brutal bg-brutal-white p-2 text-brutal-black hover-brutal-neon touch-target mobile-tap"
                >
                  <Icon name="lucide:menu" class="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" class="w-80 bg-brutal-black border-brutal-heavy">
                <SheetHeader>
                  <SheetTitle class="font-brutal text-2xl text-neon-lime">DEFI.AI</SheetTitle>
                </SheetHeader>
                <div class="mt-8 space-y-4">
                  <NuxtLink
                    to="/portfolio"
                    class="block border-brutal bg-neon-lime px-4 py-3 font-brutal text-sm uppercase text-brutal-black hover-brutal-electric mobile-tap"
                    @click="closeMobileMenu"
                  >
                    PORTFOLIO
                  </NuxtLink>
                  <NuxtLink
                    to="/strategies"
                    class="block border-brutal bg-neon-cyan px-4 py-3 font-brutal text-sm uppercase text-brutal-black hover-brutal-cyan mobile-tap"
                    @click="closeMobileMenu"
                  >
                    STRATEGIES
                  </NuxtLink>
                  <NuxtLink
                    to="/analytics"
                    class="block border-brutal bg-hot-magenta px-4 py-3 font-brutal text-sm uppercase text-brutal-white hover-brutal-magenta mobile-tap"
                    @click="closeMobileMenu"
                  >
                    ANALYTICS
                  </NuxtLink>
                  <NuxtLink
                    to="/history"
                    class="block border-brutal bg-plasma-orange px-4 py-3 font-brutal text-sm uppercase text-brutal-black hover-brutal-pink mobile-tap"
                    @click="closeMobileMenu"
                  >
                    HISTORY
                  </NuxtLink>
                  <div class="pt-4">
                    <Button
                      class="w-full border-brutal-thick bg-electric-blue px-6 py-4 font-brutal text-sm uppercase text-brutal-white shadow-brutal hover-brutal-neon mobile-tap"
                      @click="connectWallet"
                    >
                      CONNECT WALLET
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

      </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-1 nav-spacing">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="border-brutal-heavy bg-brutal-black">
      <div class="mx-auto max-w-7xl container-spacing section-spacing">
        <div class="mobile-grid gap-brutal-lg">
          <!-- Brand -->
          <div class="col-span-full md:col-span-1">
            <h3 class="font-brutal text-lg sm:text-xl text-electric-blue">DEFI.AI</h3>
            <p class="font-mono-brutal mt-2 mobile-text text-brutal-white">
              AI-POWERED DEFI DOMINATION
            </p>
          </div>

          <!-- Features -->
          <div class="md:col-span-1">
            <h4 class="font-brutal text-sm uppercase text-neon-lime">FEATURES</h4>
            <div class="mt-4 space-y-2">
              <div class="font-mono-brutal mobile-text text-brutal-white">
                AI Portfolio Optimization
              </div>
              <div class="font-mono-brutal mobile-text text-brutal-white">
                Automated Yield Farming
              </div>
              <div class="font-mono-brutal mobile-text text-brutal-white">
                Risk Management
              </div>
              <div class="font-mono-brutal mobile-text text-brutal-white">
                Gas Optimization
              </div>
            </div>
          </div>

          <!-- Protocols -->
          <div class="md:col-span-1">
            <h4 class="font-brutal text-sm uppercase text-neon-cyan">PROTOCOLS</h4>
            <div class="mt-4 space-y-2">
              <div class="font-mono-brutal mobile-text text-brutal-white">AAVE</div>
              <div class="font-mono-brutal mobile-text text-brutal-white">UNISWAP V3</div>
              <div class="font-mono-brutal mobile-text text-brutal-white">LIDO</div>
              <div class="font-mono-brutal mobile-text text-brutal-white">COMPOUND</div>
            </div>
          </div>

          <!-- Links -->
          <div class="md:col-span-1">
            <h4 class="font-brutal text-sm uppercase text-plasma-orange">LINKS</h4>
            <div class="mt-4 space-y-2">
              <NuxtLink
                to="/docs"
                class="block font-mono-brutal mobile-text text-brutal-white hover:text-acid-green transition-colors duration-100"
              >
                DOCUMENTATION
              </NuxtLink>
              <NuxtLink
                to="/support"
                class="block font-mono-brutal mobile-text text-brutal-white hover:text-electric-blue transition-colors duration-100"
              >
                SUPPORT
              </NuxtLink>
              <NuxtLink
                to="/api"
                class="block font-mono-brutal mobile-text text-brutal-white hover:text-hot-magenta transition-colors duration-100"
              >
                API ACCESS
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- Bottom Bar -->
        <div class="mt-brutal-2xl border-brutal-heavy pt-brutal-lg">
          <div class="flex flex-col items-center justify-between gap-4 md:flex-row">
            <div class="font-mono-brutal mobile-text text-brutal-white">
              © 2024 DEFI.AI. ALL RIGHTS RESERVED.
            </div>
            <div class="flex space-x-4">
              <NuxtLink
                to="/privacy"
                class="font-mono-brutal mobile-text text-brutal-white hover:text-neon-lime transition-colors duration-100"
              >
                PRIVACY
              </NuxtLink>
              <NuxtLink
                to="/terms"
                class="font-mono-brutal mobile-text text-brutal-white hover:text-electric-blue transition-colors duration-100"
              >
                TERMS
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>
