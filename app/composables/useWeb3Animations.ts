/**
 * Web3 Animation Composable
 * Provides brutal animations for Web3 wallet interactions and DeFi operations
 */

import { useBrutalMotion } from './useBrutalMotion'

export interface Web3AnimationState {
  isConnecting: boolean
  isConnected: boolean
  isTransacting: boolean
  hasError: boolean
  isSuccess: boolean
}

export const useWeb3Animations = () => {
  const { prefersReducedMotion } = useBrutalMotion()

  // Wallet connection states
  const walletState = ref<Web3AnimationState>({
    isConnecting: false,
    isConnected: false,
    isTransacting: false,
    hasError: false,
    isSuccess: false,
  })

  // Wallet connection button animation
  const walletButtonAnimation = computed(() => {
    if (prefersReducedMotion()) return {}

    if (walletState.value.isConnecting) {
      return {
        initial: { scale: 1 },
        connecting: {
          scale: [1, 0.98, 1],
          transition: {
            duration: 1000,
            ease: 'linear',
            repeat: Infinity,
          },
        },
      }
    }

    if (walletState.value.isConnected) {
      return {
        initial: { scale: 1, backgroundColor: '#ff006e' },
        connected: {
          scale: 1,
          backgroundColor: '#39ff14',
          transition: {
            duration: 300,
            ease: 'linear',
          },
        },
      }
    }

    return {
      initial: { scale: 1 },
      enter: { scale: 1 },
      hovered: {
        scale: 1.02,
        x: -2,
        y: -2,
        transition: {
          duration: 150,
          ease: 'linear',
        },
      },
      tapped: {
        scale: 0.98,
        transition: {
          duration: 100,
          ease: 'linear',
        },
      },
    }
  })

  // Transaction loading animation
  const transactionAnimation = computed(() => {
    if (prefersReducedMotion()) return {}

    return {
      initial: { opacity: 0, scale: 0.8 },
      enter: {
        opacity: 1,
        scale: 1,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
      processing: {
        rotate: [0, 90, 180, 270, 360],
        transition: {
          duration: 1200,
          ease: 'linear',
          repeat: Infinity,
        },
      },
      success: {
        scale: [1, 1.1, 1],
        transition: {
          duration: 400,
          ease: 'linear',
        },
      },
      error: {
        x: [-4, 4, -4, 4, 0],
        transition: {
          duration: 400,
          ease: 'linear',
        },
      },
    }
  })

  // Portfolio value animation
  const portfolioValueAnimation = computed(() => {
    if (prefersReducedMotion()) return {}

    return {
      initial: { opacity: 0, y: 20 },
      enter: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
      update: {
        scale: [1, 1.05, 1],
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
      increase: {
        color: '#39ff14',
        y: [-2, 0],
        transition: {
          duration: 200,
          ease: 'linear',
        },
      },
      decrease: {
        color: '#ff073a',
        y: [2, 0],
        transition: {
          duration: 200,
          ease: 'linear',
        },
      },
    }
  })

  // Gas fee indicator animation
  const gasFeeAnimation = computed(() => {
    if (prefersReducedMotion()) return {}

    return {
      initial: { opacity: 0, scale: 0.9 },
      enter: {
        opacity: 1,
        scale: 1,
        transition: {
          duration: 200,
          ease: 'linear',
        },
      },
      high: {
        backgroundColor: '#ff073a',
        scale: [1, 1.02, 1],
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
      medium: {
        backgroundColor: '#ffff00',
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
      low: {
        backgroundColor: '#39ff14',
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
    }
  })

  // Protocol connection animation
  const protocolAnimation = computed(() => {
    if (prefersReducedMotion()) return {}

    return {
      initial: { opacity: 0, x: -20 },
      enter: {
        opacity: 1,
        x: 0,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
      connecting: {
        opacity: [1, 0.7, 1],
        transition: {
          duration: 800,
          ease: 'linear',
          repeat: Infinity,
        },
      },
      connected: {
        borderColor: '#39ff14',
        scale: [1, 1.02, 1],
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
      error: {
        borderColor: '#ff073a',
        x: [-2, 2, -2, 2, 0],
        transition: {
          duration: 400,
          ease: 'linear',
        },
      },
    }
  })

  // Yield farming animation
  const yieldAnimation = computed(() => {
    if (prefersReducedMotion()) return {}

    return {
      initial: { opacity: 0, y: 10 },
      enter: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
      harvesting: {
        y: [-1, 1, -1, 1, 0],
        transition: {
          duration: 600,
          ease: 'linear',
          repeat: Infinity,
        },
      },
      harvested: {
        color: '#39ff14',
        scale: [1, 1.1, 1],
        transition: {
          duration: 400,
          ease: 'linear',
        },
      },
    }
  })

  // Notification animations
  const notificationAnimation = computed(() => {
    if (prefersReducedMotion()) return {}

    return {
      initial: { opacity: 0, x: 100, scale: 0.9 },
      enter: {
        opacity: 1,
        x: 0,
        scale: 1,
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
      leave: {
        opacity: 0,
        x: 100,
        scale: 0.9,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
      shake: {
        x: [-2, 2, -2, 2, 0],
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
    }
  })

  // State management functions
  const setWalletConnecting = () => {
    walletState.value = {
      isConnecting: true,
      isConnected: false,
      isTransacting: false,
      hasError: false,
      isSuccess: false,
    }
  }

  const setWalletConnected = () => {
    walletState.value = {
      isConnecting: false,
      isConnected: true,
      isTransacting: false,
      hasError: false,
      isSuccess: true,
    }
  }

  const setWalletError = () => {
    walletState.value = {
      isConnecting: false,
      isConnected: false,
      isTransacting: false,
      hasError: true,
      isSuccess: false,
    }
  }

  const setTransacting = () => {
    walletState.value.isTransacting = true
    walletState.value.hasError = false
    walletState.value.isSuccess = false
  }

  const setTransactionSuccess = () => {
    walletState.value.isTransacting = false
    walletState.value.isSuccess = true
    walletState.value.hasError = false
  }

  const setTransactionError = () => {
    walletState.value.isTransacting = false
    walletState.value.hasError = true
    walletState.value.isSuccess = false
  }

  const resetState = () => {
    walletState.value = {
      isConnecting: false,
      isConnected: false,
      isTransacting: false,
      hasError: false,
      isSuccess: false,
    }
  }

  return {
    // State
    walletState: readonly(walletState),
    
    // Animations
    walletButtonAnimation,
    transactionAnimation,
    portfolioValueAnimation,
    gasFeeAnimation,
    protocolAnimation,
    yieldAnimation,
    notificationAnimation,
    
    // State management
    setWalletConnecting,
    setWalletConnected,
    setWalletError,
    setTransacting,
    setTransactionSuccess,
    setTransactionError,
    resetState,
  }
}
