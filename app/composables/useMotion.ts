/**
 * Core Motion Composable
 * Provides utilities for @vueuse/motion integration with performance optimizations
 */

import type { MotionVariants } from '@vueuse/motion';
import { useMotion } from '@vueuse/motion';
import type { Ref } from 'vue';

export interface MotionOptions {
  immediate?: boolean;
  delay?: number;
  stagger?: number;
  once?: boolean;
}

export const useEnhancedMotion = () => {
  // Performance-optimized motion utility
  const createMotion = (
    target: Ref<HTMLElement | undefined>,
    variants: MotionVariants,
    options: MotionOptions = {}
  ) => {
    const {
      immediate: _immediate = false,
      delay: _delay = 0,
      once: _once = true,
    } = options;

    // Only create motion if element exists and motion is supported
    if (!target.value) return null;

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia(
      '(prefers-reduced-motion: reduce)'
    ).matches;

    if (prefersReducedMotion) {
      // Skip animations for users who prefer reduced motion
      return null;
    }

    return useMotion(target, variants);
  };

  // Intersection Observer for scroll-triggered animations
  const useScrollMotion = (
    target: Ref<HTMLElement | undefined>,
    variants: MotionVariants,
    options: MotionOptions = {}
  ) => {
    const { once = true } = options;

    if (!target.value) return null;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const motion = createMotion(target, variants, options);
            if (motion) {
              motion.apply('enter');
            }

            if (once) {
              observer.unobserve(entry.target);
            }
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (target.value) {
      observer.observe(target.value);
    }

    return observer;
  };

  // Stagger animation utility for lists
  const useStaggerMotion = (
    targets: Ref<HTMLElement[]>,
    variants: MotionVariants,
    staggerDelay = 100
  ) => {
    if (!targets.value?.length) return [];

    return targets.value.map((target, index) => {
      const targetRef = ref(target);
      const delayedVariants = {
        ...variants,
        enter: {
          ...variants.enter,
          transition: {
            ...variants.enter?.transition,
            delay: index * staggerDelay,
          },
        },
      };

      return createMotion(targetRef, delayedVariants);
    });
  };

  // Hover motion utility
  const useHoverMotion = (
    target: Ref<HTMLElement | undefined>,
    hoverVariants: MotionVariants
  ) => {
    if (!target.value) return null;

    const motion = createMotion(target, hoverVariants);

    const handleMouseEnter = () => {
      motion?.apply('hover');
    };

    const handleMouseLeave = () => {
      motion?.apply('initial');
    };

    if (target.value) {
      target.value.addEventListener('mouseenter', handleMouseEnter);
      target.value.addEventListener('mouseleave', handleMouseLeave);
    }

    return {
      motion,
      cleanup: () => {
        if (target.value) {
          target.value.removeEventListener('mouseenter', handleMouseEnter);
          target.value.removeEventListener('mouseleave', handleMouseLeave);
        }
      },
    };
  };

  // Touch motion utility for mobile
  const useTouchMotion = (
    target: Ref<HTMLElement | undefined>,
    touchVariants: MotionVariants
  ) => {
    if (!target.value) return null;

    const motion = createMotion(target, touchVariants);

    const handleTouchStart = () => {
      motion?.apply('tap');
    };

    const handleTouchEnd = () => {
      motion?.apply('initial');
    };

    if (target.value) {
      target.value.addEventListener('touchstart', handleTouchStart);
      target.value.addEventListener('touchend', handleTouchEnd);
    }

    return {
      motion,
      cleanup: () => {
        if (target.value) {
          target.value.removeEventListener('touchstart', handleTouchStart);
          target.value.removeEventListener('touchend', handleTouchEnd);
        }
      },
    };
  };

  // Performance monitoring
  const monitorMotionPerformance = () => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'measure' && entry.name.includes('motion')) {
            console.log(
              `Motion performance: ${entry.name} took ${entry.duration}ms`
            );
          }
        });
      });

      observer.observe({ entryTypes: ['measure'] });
      return observer;
    }
    return null;
  };

  return {
    createMotion,
    useScrollMotion,
    useStaggerMotion,
    useHoverMotion,
    useTouchMotion,
    monitorMotionPerformance,
  };
};

// Utility function to check if device supports hardware acceleration
export const supportsHardwareAcceleration = (): boolean => {
  if (typeof window === 'undefined') return false;

  const canvas = document.createElement('canvas');
  const gl =
    canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  return !!gl;
};

// Utility to optimize motion for low-end devices
export const getOptimizedMotionConfig = () => {
  const isLowEndDevice =
    !supportsHardwareAcceleration() ||
    (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4);

  return {
    duration: isLowEndDevice ? 150 : 250,
    useTransform: !isLowEndDevice,
    enableStagger: !isLowEndDevice,
  };
};
