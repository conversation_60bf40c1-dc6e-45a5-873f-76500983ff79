/**
 * Page Transition Composable
 * Provides brutal page transition animations for Nuxt 3 routing
 */

import { useBrutalMotion } from './useBrutalMotion'

export interface PageTransitionConfig {
  name: string
  mode: 'out-in' | 'in-out' | 'default'
  duration: number
  enterFromClass: string
  enterActiveClass: string
  enterToClass: string
  leaveFromClass: string
  leaveActiveClass: string
  leaveToClass: string
}

export const usePageTransitions = () => {
  const { prefersReducedMotion } = useBrutalMotion()

  // Brutal slide transition (default)
  const brutalSlideTransition: PageTransitionConfig = {
    name: 'brutal-slide',
    mode: 'out-in',
    duration: 300,
    enterFromClass: 'opacity-0 translate-x-full',
    enterActiveClass: 'transition-all duration-300 ease-linear',
    enterToClass: 'opacity-100 translate-x-0',
    leaveFromClass: 'opacity-100 translate-x-0',
    leaveActiveClass: 'transition-all duration-300 ease-linear',
    leaveToClass: 'opacity-0 -translate-x-full',
  }

  // Brutal fade transition
  const brutalFadeTransition: PageTransitionConfig = {
    name: 'brutal-fade',
    mode: 'out-in',
    duration: 250,
    enterFromClass: 'opacity-0',
    enterActiveClass: 'transition-opacity duration-250 ease-linear',
    enterToClass: 'opacity-100',
    leaveFromClass: 'opacity-100',
    leaveActiveClass: 'transition-opacity duration-250 ease-linear',
    leaveToClass: 'opacity-0',
  }

  // Brutal scale transition
  const brutalScaleTransition: PageTransitionConfig = {
    name: 'brutal-scale',
    mode: 'out-in',
    duration: 300,
    enterFromClass: 'opacity-0 scale-95',
    enterActiveClass: 'transition-all duration-300 ease-linear',
    enterToClass: 'opacity-100 scale-100',
    leaveFromClass: 'opacity-100 scale-100',
    leaveActiveClass: 'transition-all duration-300 ease-linear',
    leaveToClass: 'opacity-0 scale-105',
  }

  // Brutal vertical slide transition
  const brutalVerticalTransition: PageTransitionConfig = {
    name: 'brutal-vertical',
    mode: 'out-in',
    duration: 350,
    enterFromClass: 'opacity-0 translate-y-8',
    enterActiveClass: 'transition-all duration-350 ease-linear',
    enterToClass: 'opacity-100 translate-y-0',
    leaveFromClass: 'opacity-100 translate-y-0',
    leaveActiveClass: 'transition-all duration-350 ease-linear',
    leaveToClass: 'opacity-0 -translate-y-8',
  }

  // No transition for reduced motion users
  const noTransition: PageTransitionConfig = {
    name: 'no-transition',
    mode: 'default',
    duration: 0,
    enterFromClass: '',
    enterActiveClass: '',
    enterToClass: '',
    leaveFromClass: '',
    leaveActiveClass: '',
    leaveToClass: '',
  }

  // Get appropriate transition based on user preference
  const getTransition = (type: 'slide' | 'fade' | 'scale' | 'vertical' = 'slide'): PageTransitionConfig => {
    if (prefersReducedMotion()) {
      return noTransition
    }

    switch (type) {
      case 'fade':
        return brutalFadeTransition
      case 'scale':
        return brutalScaleTransition
      case 'vertical':
        return brutalVerticalTransition
      default:
        return brutalSlideTransition
    }
  }

  // Route-specific transitions
  const getRouteTransition = (to: string, from: string): PageTransitionConfig => {
    if (prefersReducedMotion()) {
      return noTransition
    }

    // Portfolio pages use slide transitions
    if (to.includes('/portfolio') || from.includes('/portfolio')) {
      return brutalSlideTransition
    }

    // Analytics pages use scale transitions
    if (to.includes('/analytics') || from.includes('/analytics')) {
      return brutalScaleTransition
    }

    // Strategy pages use vertical transitions
    if (to.includes('/strategies') || from.includes('/strategies')) {
      return brutalVerticalTransition
    }

    // Default to fade for other routes
    return brutalFadeTransition
  }

  // Page enter animation hook
  const onPageEnter = (el: Element) => {
    if (prefersReducedMotion()) return

    // Add brutal entrance animation
    el.classList.add('animate-brutal-enter')
    
    // Remove animation class after completion
    setTimeout(() => {
      el.classList.remove('animate-brutal-enter')
    }, 300)
  }

  // Page leave animation hook
  const onPageLeave = (el: Element) => {
    if (prefersReducedMotion()) return

    // Add brutal exit animation
    el.classList.add('animate-brutal-exit')
  }

  // Loading state animation
  const createLoadingAnimation = () => {
    if (prefersReducedMotion()) return {}

    return {
      initial: { opacity: 0, scale: 0.8 },
      enter: { 
        opacity: 1, 
        scale: 1,
        transition: {
          duration: 200,
          ease: 'linear',
        },
      },
      leave: {
        opacity: 0,
        scale: 1.1,
        transition: {
          duration: 150,
          ease: 'linear',
        },
      },
    }
  }

  // Error state animation
  const createErrorAnimation = () => {
    if (prefersReducedMotion()) return {}

    return {
      initial: { opacity: 0, x: -20 },
      enter: { 
        opacity: 1, 
        x: 0,
        transition: {
          duration: 250,
          ease: 'linear',
        },
      },
      shake: {
        x: [-2, 2, -2, 2, 0],
        transition: {
          duration: 400,
          ease: 'linear',
        },
      },
    }
  }

  // Success state animation
  const createSuccessAnimation = () => {
    if (prefersReducedMotion()) return {}

    return {
      initial: { opacity: 0, y: 20 },
      enter: { 
        opacity: 1, 
        y: 0,
        transition: {
          duration: 300,
          ease: 'linear',
        },
      },
      bounce: {
        y: [-4, 0],
        transition: {
          duration: 200,
          ease: 'linear',
        },
      },
    }
  }

  return {
    // Transition configs
    brutalSlideTransition,
    brutalFadeTransition,
    brutalScaleTransition,
    brutalVerticalTransition,
    noTransition,
    
    // Utilities
    getTransition,
    getRouteTransition,
    onPageEnter,
    onPageLeave,
    
    // State animations
    createLoadingAnimation,
    createErrorAnimation,
    createSuccessAnimation,
  }
}
