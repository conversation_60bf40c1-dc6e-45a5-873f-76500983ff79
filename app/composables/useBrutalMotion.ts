/**
 * Neo-Brutalism Motion Composable
 * Provides sharp, mechanical animations that align with brutal design aesthetic
 */

export interface BrutalMotionConfig {
  duration: number;
  easing: string;
  stagger?: number;
  delay?: number;
}

export interface MotionVariant {
  initial?: Record<string, any>;
  enter?: Record<string, any>;
  visible?: Record<string, any>;
  hovered?: Record<string, any>;
  tapped?: Record<string, any>;
  focused?: Record<string, any>;
  [key: string]: any;
}

export const useBrutalMotion = () => {
  // Core brutal motion configuration
  const brutalConfig: BrutalMotionConfig = {
    duration: 250, // Fast, snappy animations
    easing: 'linear', // No smooth curves - sharp mechanical movement
    stagger: 100, // Stagger delay for lists
    delay: 0,
  };

  // Reduced motion configuration for accessibility
  const reducedMotionConfig: BrutalMotionConfig = {
    duration: 0,
    easing: 'linear',
    stagger: 0,
    delay: 0,
  };

  // Check for user's motion preference
  const prefersReducedMotion = () => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }
    return false;
  };

  // Get appropriate config based on user preference
  const getMotionConfig = (): BrutalMotionConfig => {
    return prefersReducedMotion() ? reducedMotionConfig : brutalConfig;
  };

  // Brutal entrance animations
  const brutalEntranceVariants: MotionVariant = {
    initial: {
      opacity: 0,
      y: 20,
    },
    enter: {
      opacity: 1,
      y: 0,
      transition: {
        duration: getMotionConfig().duration,
        ease: getMotionConfig().easing,
      },
    },
  };

  // Brutal slide animations
  const brutalSlideLeft: MotionVariant = {
    initial: {
      opacity: 0,
      x: -40,
    },
    enter: {
      opacity: 1,
      x: 0,
      transition: {
        duration: getMotionConfig().duration,
        ease: getMotionConfig().easing,
      },
    },
  };

  const brutalSlideRight: MotionVariant = {
    initial: {
      opacity: 0,
      x: 40,
    },
    enter: {
      opacity: 1,
      x: 0,
      transition: {
        duration: getMotionConfig().duration,
        ease: getMotionConfig().easing,
      },
    },
  };

  // Brutal pop animation for buttons and interactive elements
  const brutalPop: MotionVariant = {
    initial: {
      opacity: 0,
      scale: 0.8,
    },
    enter: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: getMotionConfig().duration,
        ease: getMotionConfig().easing,
      },
    },
    tapped: {
      scale: 0.95,
      transition: {
        duration: 100,
        ease: 'linear',
      },
    },
  };

  // Brutal fade animation
  const brutalFade: MotionVariant = {
    initial: {
      opacity: 0,
    },
    enter: {
      opacity: 1,
      transition: {
        duration: getMotionConfig().duration,
        ease: getMotionConfig().easing,
      },
    },
  };

  // Brutal bounce for emphasis (used sparingly)
  const brutalBounce: MotionVariant = {
    initial: {
      opacity: 0,
      y: -10,
    },
    enter: {
      opacity: 1,
      y: 0,
      transition: {
        duration: getMotionConfig().duration * 1.5,
        ease: 'linear',
      },
    },
  };

  // Stagger animation for lists and grids
  const createBrutalStagger = (_itemCount?: number) => {
    const config = getMotionConfig();
    return {
      enter: {
        transition: {
          staggerChildren: config.stagger,
          delayChildren: config.delay,
        },
      },
    };
  };

  // Hover animations with brutal shadow effects
  const brutalHover: MotionVariant = {
    initial: {
      x: 0,
      y: 0,
    },
    hovered: {
      x: -2,
      y: -2,
      transition: {
        duration: 150,
        ease: 'linear',
      },
    },
  };

  // Mobile-specific touch feedback
  const brutalTouch: MotionVariant = {
    initial: {
      scale: 1,
    },
    tapped: {
      scale: 0.98,
      transition: {
        duration: 100,
        ease: 'linear',
      },
    },
  };

  // Page transition animations
  const brutalPageTransition: MotionVariant = {
    initial: {
      opacity: 0,
      x: 100,
    },
    enter: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 300,
        ease: 'linear',
      },
    },
    leave: {
      opacity: 0,
      x: -100,
      transition: {
        duration: 300,
        ease: 'linear',
      },
    },
  };

  return {
    // Configuration
    brutalConfig,
    getMotionConfig,
    prefersReducedMotion,

    // Animation variants
    brutalEntranceVariants,
    brutalSlideLeft,
    brutalSlideRight,
    brutalPop,
    brutalFade,
    brutalBounce,
    brutalHover,
    brutalTouch,
    brutalPageTransition,

    // Utilities
    createBrutalStagger,
  };
};
