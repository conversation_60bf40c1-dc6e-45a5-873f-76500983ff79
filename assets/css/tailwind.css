@import "tailwindcss";
@import "tw-animate-css";

@theme {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Neo-Brutalism Color Palette - Enhanced */
  --color-neon-lime: #00ff41;
  --color-neon-pink: #ff0080;
  --color-neon-cyan: #00ffff;
  --color-neon-orange: #ff6b00;
  --color-brutal-black: #000000;
  --color-brutal-charcoal: #1a1a1a;
  --color-brutal-white: #ffffff;

  /* Additional Vibrant Colors */
  --color-electric-blue: #0066ff;
  --color-hot-magenta: #ff006e;
  --color-acid-green: #39ff14;
  --color-laser-red: #ff073a;
  --color-cyber-purple: #8a2be2;
  --color-toxic-yellow: #ffff00;
  --color-neon-violet: #9d00ff;
  --color-plasma-orange: #ff4500;
}

:root {
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Neo-Brutalism Typography */
  .font-brutal {
    font-family: "Inter", system-ui, -apple-system, sans-serif;
    font-weight: 900;
  }

  .font-mono-brutal {
    font-family: "JetBrains Mono", "Courier New", monospace;
    font-weight: 700;
  }
}

@layer utilities {
  /* Neo-Brutalism Borders */
  .border-brutal {
    border-width: 4px;
    border-color: var(--color-brutal-black);
    border-style: solid;
  }

  .border-brutal-thick {
    border-width: 6px;
    border-color: var(--color-brutal-black);
    border-style: solid;
  }

  .border-brutal-heavy {
    border-width: 8px;
    border-color: var(--color-brutal-black);
    border-style: solid;
  }

  .border-brutal-heavy-top {
    border-top-width: 8px;
    border-top-color: var(--color-brutal-black);
    border-top-style: solid;
  }

  .border-brutal-heavy-bottom {
    border-bottom-width: 8px;
    border-bottom-color: var(--color-brutal-black);
    border-bottom-style: solid;
  }

  /* Neo-Brutalism Shadows */
  .shadow-brutal {
    box-shadow: 8px 8px 0px var(--color-brutal-black);
  }

  .shadow-brutal-lg {
    box-shadow: 12px 12px 0px var(--color-brutal-black);
  }

  .shadow-brutal-neon-lime {
    box-shadow: 8px 8px 0px var(--color-neon-lime);
  }

  .shadow-brutal-neon-pink {
    box-shadow: 8px 8px 0px var(--color-neon-pink);
  }

  .shadow-brutal-neon-cyan {
    box-shadow: 8px 8px 0px var(--color-neon-cyan);
  }

  .shadow-brutal-neon-orange {
    box-shadow: 8px 8px 0px var(--color-neon-orange);
  }

  /* Additional Vibrant Shadow Utilities */
  .shadow-brutal-electric-blue {
    box-shadow: 8px 8px 0px var(--color-electric-blue);
  }

  .shadow-brutal-hot-magenta {
    box-shadow: 8px 8px 0px var(--color-hot-magenta);
  }

  .shadow-brutal-acid-green {
    box-shadow: 8px 8px 0px var(--color-acid-green);
  }

  .shadow-brutal-laser-red {
    box-shadow: 8px 8px 0px var(--color-laser-red);
  }

  .shadow-brutal-cyber-purple {
    box-shadow: 8px 8px 0px var(--color-cyber-purple);
  }

  .shadow-brutal-toxic-yellow {
    box-shadow: 8px 8px 0px var(--color-toxic-yellow);
  }

  .shadow-brutal-neon-violet {
    box-shadow: 8px 8px 0px var(--color-neon-violet);
  }

  .shadow-brutal-plasma-orange {
    box-shadow: 8px 8px 0px var(--color-plasma-orange);
  }

  .shadow-brutal-cyber-purple {
    box-shadow: 8px 8px 0px var(--color-cyber-purple);
  }

  /* Neo-Brutalism Colors */
  .bg-neon-lime {
    background-color: var(--color-neon-lime);
  }

  .bg-neon-pink {
    background-color: var(--color-neon-pink);
  }

  .bg-neon-cyan {
    background-color: var(--color-neon-cyan);
  }

  .bg-neon-orange {
    background-color: var(--color-neon-orange);
  }

  .bg-brutal-black {
    background-color: var(--color-brutal-black);
  }

  .bg-brutal-charcoal {
    background-color: var(--color-brutal-charcoal);
  }

  .text-neon-lime {
    color: var(--color-neon-lime);
  }

  .text-neon-pink {
    color: var(--color-neon-pink);
  }

  .text-neon-cyan {
    color: var(--color-neon-cyan);
  }

  .text-neon-orange {
    color: var(--color-neon-orange);
  }

  .text-brutal-black {
    color: var(--color-brutal-black);
  }

  .text-brutal-white {
    color: var(--color-brutal-white);
  }

  /* Additional Vibrant Text Colors */
  .text-electric-blue {
    color: var(--color-electric-blue);
  }

  .text-hot-magenta {
    color: var(--color-hot-magenta);
  }

  .text-acid-green {
    color: var(--color-acid-green);
  }

  .text-laser-red {
    color: var(--color-laser-red);
  }

  .text-cyber-purple {
    color: var(--color-cyber-purple);
  }

  .text-toxic-yellow {
    color: var(--color-toxic-yellow);
  }

  .text-neon-violet {
    color: var(--color-neon-violet);
  }

  .text-plasma-orange {
    color: var(--color-plasma-orange);
  }

  /* Additional Vibrant Background Colors */
  .bg-electric-blue {
    background-color: var(--color-electric-blue);
  }

  .bg-hot-magenta {
    background-color: var(--color-hot-magenta);
  }

  .bg-acid-green {
    background-color: var(--color-acid-green);
  }

  .bg-laser-red {
    background-color: var(--color-laser-red);
  }

  .bg-cyber-purple {
    background-color: var(--color-cyber-purple);
  }

  .bg-toxic-yellow {
    background-color: var(--color-toxic-yellow);
  }

  .bg-neon-violet {
    background-color: var(--color-neon-violet);
  }

  .bg-plasma-orange {
    background-color: var(--color-plasma-orange);
  }

  /* Neo-Brutalism Animations */
  .glitch {
    animation: glitch 2s infinite;
  }

  @keyframes glitch {
    0%,
    100% {
      transform: translate(0);
    }
    10% {
      transform: translate(-2px, -2px);
    }
    20% {
      transform: translate(2px, 2px);
    }
    30% {
      transform: translate(-2px, 2px);
    }
    40% {
      transform: translate(2px, -2px);
    }
    50% {
      transform: translate(-2px, -2px);
    }
    60% {
      transform: translate(2px, 2px);
    }
    70% {
      transform: translate(-2px, 2px);
    }
    80% {
      transform: translate(2px, -2px);
    }
    90% {
      transform: translate(-2px, -2px);
    }
  }

  /* Terminal-style typing animation */
  .typing {
    overflow: hidden;
    border-right: 3px solid var(--color-neon-lime);
    white-space: nowrap;
    animation:
      typing 3.5s steps(40, end),
      blink-caret 0.75s step-end infinite;
  }

  @keyframes typing {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }

  @keyframes blink-caret {
    from,
    to {
      border-color: transparent;
    }
    50% {
      border-color: var(--color-neon-lime);
    }
  }

  /* Enhanced Brutal hover effects */
  .hover-brutal:hover {
    transform: translate(-4px, -4px);
    box-shadow: 12px 12px 0px var(--color-brutal-black);
    transition: all 0.1s linear;
  }

  .hover-brutal-neon:hover {
    transform: translate(-4px, -4px);
    box-shadow: 12px 12px 0px var(--color-neon-lime);
    transition: all 0.1s linear;
  }

  .hover-brutal-pink:hover {
    transform: translate(-4px, -4px);
    box-shadow: 12px 12px 0px var(--color-neon-pink);
    transition: all 0.1s linear;
  }

  .hover-brutal-cyan:hover {
    transform: translate(-4px, -4px);
    box-shadow: 12px 12px 0px var(--color-neon-cyan);
    transition: all 0.1s linear;
  }

  .hover-brutal-electric:hover {
    transform: translate(-4px, -4px);
    box-shadow: 12px 12px 0px var(--color-electric-blue);
    transition: all 0.1s linear;
  }

  .hover-brutal-magenta:hover {
    transform: translate(-4px, -4px);
    box-shadow: 12px 12px 0px var(--color-hot-magenta);
    transition: all 0.1s linear;
  }

  .hover-invert:hover {
    filter: invert(1);
    transition: all 0.1s linear;
  }

  .hover-glitch:hover {
    animation: glitch 0.5s infinite;
  }

  /* Brutal Form Elements */
  .input-brutal {
    border-radius: 0 !important;
    border-width: 4px;
    border-color: var(--color-brutal-black);
    border-style: solid;
  }

  .checkbox-brutal {
    border-radius: 0 !important;
    border-width: 4px;
    border-color: var(--color-brutal-black);
    border-style: solid;
  }

  .range-brutal {
    border-radius: 0 !important;
    -webkit-appearance: none;
    appearance: none;
    height: 16px;
    background: var(--color-brutal-charcoal);
    border: 4px solid var(--color-brutal-black);
  }

  .range-brutal::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    background: var(--color-neon-lime);
    border: 4px solid var(--color-brutal-black);
    border-radius: 0;
    cursor: pointer;
  }

  .range-brutal::-moz-range-thumb {
    width: 24px;
    height: 24px;
    background: var(--color-neon-lime);
    border: 4px solid var(--color-brutal-black);
    border-radius: 0;
    cursor: pointer;
  }

  /* Override any rounded corners globally */
  .brutal-override {
    border-radius: 0 !important;
  }

  /* Brutal Spacing System - aligned with 4px/6px/8px grid */
  .p-brutal {
    padding: 8px;
  }

  .px-brutal {
    padding-left: 8px;
    padding-right: 8px;
  }

  .py-brutal {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .p-brutal-lg {
    padding: 12px;
  }

  .px-brutal-lg {
    padding-left: 12px;
    padding-right: 12px;
  }

  .py-brutal-lg {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .p-brutal-xl {
    padding: 16px;
  }

  .px-brutal-xl {
    padding-left: 16px;
    padding-right: 16px;
  }

  .py-brutal-xl {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  /* Mobile-First Responsive Utilities */
  .mobile-stack {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  @media (min-width: 640px) {
    .mobile-stack {
      flex-direction: row;
    }
  }

  .mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (min-width: 640px) {
    .mobile-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .mobile-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (min-width: 1280px) {
    .mobile-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  .mobile-text {
    font-size: 0.875rem;
  }

  @media (min-width: 640px) {
    .mobile-text {
      font-size: 1rem;
    }
  }

  @media (min-width: 1024px) {
    .mobile-text {
      font-size: 1.125rem;
    }
  }

  .mobile-heading {
    font-size: 1.5rem;
  }

  @media (min-width: 640px) {
    .mobile-heading {
      font-size: 1.875rem;
    }
  }

  @media (min-width: 768px) {
    .mobile-heading {
      font-size: 2.25rem;
    }
  }

  @media (min-width: 1024px) {
    .mobile-heading {
      font-size: 3rem;
    }
  }

  @media (min-width: 1280px) {
    .mobile-heading {
      font-size: 3.75rem;
    }
  }

  .mobile-padding {
    padding: 1rem;
  }

  @media (min-width: 640px) {
    .mobile-padding {
      padding: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .mobile-padding {
      padding: 2rem;
    }
  }

  .mobile-margin {
    margin: 1rem;
  }

  @media (min-width: 640px) {
    .mobile-margin {
      margin: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .mobile-margin {
      margin: 2rem;
    }
  }

  /* Touch-Friendly Interactive Elements */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background-color: var(--color-brutal-white);
    border-bottom-width: 8px;
    border-bottom-color: var(--color-brutal-black);
    border-bottom-style: solid;
  }

  /* Navigation Spacing Utilities */
  .nav-spacing {
    padding-top: 80px; /* 64px nav height + 16px spacing */
  }

  @media (min-width: 640px) {
    .nav-spacing {
      padding-top: 88px; /* 64px nav height + 24px spacing */
    }
  }

  @media (min-width: 1024px) {
    .nav-spacing {
      padding-top: 96px; /* 64px nav height + 32px spacing */
    }
  }

  .mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 40;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }

  /* Enhanced Brutal Animations for Mobile */
  .mobile-hover:active {
    transform: translate(-2px, -2px);
    box-shadow: 6px 6px 0px var(--color-brutal-black);
    transition: all 0.1s linear;
  }

  .mobile-tap:active {
    transform: scale(0.95);
    transition: transform 0.1s linear;
  }

  /* Enhanced Brutal Margins - Mobile-First Responsive */
  .m-brutal {
    margin: 8px;
  }

  .mx-brutal {
    margin-left: 8px;
    margin-right: 8px;
  }

  .my-brutal {
    margin-top: 8px;
    margin-bottom: 8px;
  }

  .mt-brutal {
    margin-top: 8px;
  }

  .mb-brutal {
    margin-bottom: 8px;
  }

  .ml-brutal {
    margin-left: 8px;
  }

  .mr-brutal {
    margin-right: 8px;
  }

  /* Large Brutal Margins */
  .m-brutal-lg {
    margin: 12px;
  }

  .mx-brutal-lg {
    margin-left: 12px;
    margin-right: 12px;
  }

  .my-brutal-lg {
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .mt-brutal-lg {
    margin-top: 12px;
  }

  .mb-brutal-lg {
    margin-bottom: 12px;
  }

  .ml-brutal-lg {
    margin-left: 12px;
  }

  .mr-brutal-lg {
    margin-right: 12px;
  }

  /* Extra Large Brutal Margins */
  .m-brutal-xl {
    margin: 16px;
  }

  .mx-brutal-xl {
    margin-left: 16px;
    margin-right: 16px;
  }

  .my-brutal-xl {
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .mt-brutal-xl {
    margin-top: 16px;
  }

  .mb-brutal-xl {
    margin-bottom: 16px;
  }

  .ml-brutal-xl {
    margin-left: 16px;
  }

  .mr-brutal-xl {
    margin-right: 16px;
  }

  /* 2XL Brutal Margins */
  .m-brutal-2xl {
    margin: 24px;
  }

  .mx-brutal-2xl {
    margin-left: 24px;
    margin-right: 24px;
  }

  .my-brutal-2xl {
    margin-top: 24px;
    margin-bottom: 24px;
  }

  .mt-brutal-2xl {
    margin-top: 24px;
  }

  .mb-brutal-2xl {
    margin-bottom: 24px;
  }

  .ml-brutal-2xl {
    margin-left: 24px;
  }

  .mr-brutal-2xl {
    margin-right: 24px;
  }

  /* Mobile-First Responsive Margins */
  .mobile-margin-x {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  @media (min-width: 640px) {
    .mobile-margin-x {
      margin-left: 1.5rem;
      margin-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .mobile-margin-x {
      margin-left: 2rem;
      margin-right: 2rem;
    }
  }

  .mobile-margin-y {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  @media (min-width: 640px) {
    .mobile-margin-y {
      margin-top: 1.5rem;
      margin-bottom: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .mobile-margin-y {
      margin-top: 2rem;
      margin-bottom: 2rem;
    }
  }

  /* Section Spacing Utilities */
  .section-spacing {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  @media (min-width: 640px) {
    .section-spacing {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }
  }

  @media (min-width: 1024px) {
    .section-spacing {
      padding-top: 5rem;
      padding-bottom: 5rem;
    }
  }

  /* Container Spacing */
  .container-spacing {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  @media (min-width: 640px) {
    .container-spacing {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-spacing {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Brutal Gaps */
  .gap-brutal {
    gap: 8px;
  }

  .gap-brutal-lg {
    gap: 12px;
  }

  .gap-brutal-xl {
    gap: 16px;
  }

  /* Brutal Space-Y utilities */
  .space-y-brutal > * + * {
    margin-top: 8px;
  }

  .space-y-brutal-lg > * + * {
    margin-top: 12px;
  }

  .space-y-brutal-xl > * + * {
    margin-top: 16px;
  }

  /* Neo-Brutalism Motion Utilities */
  .brutal-enter {
    animation: brutal-enter 250ms linear forwards;
  }

  .brutal-exit {
    animation: brutal-exit 250ms linear forwards;
  }

  .brutal-slide-left {
    animation: brutal-slide-left 250ms linear forwards;
  }

  .brutal-slide-right {
    animation: brutal-slide-right 250ms linear forwards;
  }

  .brutal-pop {
    animation: brutal-pop 250ms linear forwards;
  }

  .brutal-shake {
    animation: brutal-shake 400ms linear forwards;
  }

  .brutal-pulse {
    animation: brutal-pulse 1000ms linear infinite;
  }

  .brutal-loading {
    animation: brutal-loading 1000ms linear infinite;
  }

  /* Motion-safe variants for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .brutal-enter,
    .brutal-exit,
    .brutal-slide-left,
    .brutal-slide-right,
    .brutal-pop,
    .brutal-shake,
    .brutal-pulse,
    .brutal-loading {
      animation: none;
    }

    .mobile-tap:active {
      transform: scale(1);
    }
  }
}

/* Neo-Brutalism Keyframe Animations */
@keyframes brutal-enter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes brutal-exit {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

@keyframes brutal-slide-left {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes brutal-slide-right {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes brutal-pop {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes brutal-shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-4px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(4px);
  }
}

@keyframes brutal-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes brutal-loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
