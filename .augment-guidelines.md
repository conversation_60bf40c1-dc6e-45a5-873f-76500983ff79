# Biome Configuration
- Use Biome schema version 2.0.0-beta.5 (https://biomejs.dev/schemas/2.0.0-beta.5/schema.json) for configuration.

# Package Managers / Runners
- User prefers using bunx or bun x instead of npx for running npm packages.

# Dependency Prioritization
- When developing features, prioritize ecosystem-specific packages in order: VueUse for Vue composables, UnJS for universal utilities, Nuxt modules for framework integrations, then generic npm packages as last resort.

# Design System
- User wants to use shadcn-vue-mcp for the design system.
- shadcn-nuxt has been successfully integrated into the DeFi agent project.
- Use `bunx --bun shadcn-vue@latest add [component]` syntax for adding shadcn components using cli.

# Project Management
- User prefers dual project management approach: Linear for high-level project tracking/stakeholder communication, TaskMaster MCP for detailed technical task breakdown, with integration between both systems for synchronized status updates and traceability.
- User prefers TaskMaster MCP tools for project management and wants to generate tasks from PRD documents with complexity analysis and structured workflows.

# Infrastructure
- User prefers NuxtHub (Nuxt.js with Cloudflare infrastructure) over Next.js/AWS stack for the DeFi agent project, including serverless functions and edge deployment.
- User prefers Cloudflare full suite (D1/KV/R2/AI).

# DeFi Project - MVP Scope
- User prefers streamlined MVP approach for DeFi project: Web3-only authentication (no OAuth/JWT/RBAC/KYC), single-chain Ethereum focus, AI-centric features prioritized.
- Removed mobile app and multi-sig wallet features.
- User wants to use shadcn-nuxt components.

# DeFi Agent UI Development
- For DeFi agent UI development: use Nuxt 3 conventions with layouts/ and pages/ directories, Vue 3 Composition API with `<script setup>`, shadcn-nuxt as primary design system, focus on responsive design and accessibility standards.
- Apply Neo-Brutalism design patterns from app/pages/design-system.vue for all future DeFi agent UI development: use enhanced vibrant color palette (electric-blue, hot-magenta, acid-green, etc.), shadcn-nuxt components with brutal styling, mobile-first responsive utilities (mobile-stack, mobile-grid, mobile-text, etc.), touch-friendly interactions, responsive typography scaling, enhanced hover states with vibrant shadows, and Vue 3 Composition API with script setup.
- For DeFi agent UI: use Neo-Brutalism design with sharp/mechanical animations (not smooth curves), implement comprehensive hover states with transform/shadow effects, prioritize shadcn-vue-mcp components over basic HTML, and use @vueuse/motion for animations aligned with brutal aesthetic.
- For Neo-Brutalism design in DeFi agent: remove all border-radius properties (set to 0), replace curved elements with sharp rectangular edges, maintain aggressive industrial look with hard angles across all UI components.
- User prefers Card component padding to align with brutal design grid system using 4px/6px/8px spacing increments for Neo-Brutalism aesthetic consistency.
- For DeFi agent Neo-Brutalism design: use high-contrast vibrant colors (bright yellows, electric blues, hot pinks, neon greens) for retro aesthetic, implement mobile responsiveness with Tailwind breakpoints, use browser MCP tools for UI testing and validation across screen sizes.
- User prefers using Context7 MCP for retrieving Tailwind CSS documentation when needed.
- For DeFi agent: use consistent margin/padding standards with Neo-Brutalism 4px/6px/8px spacing increments, mobile-first responsive design with Tailwind breakpoints, and shadcn-nuxt components with consistent spacing props across all pages.
- For mobile responsiveness: transform tables into card-based layouts below 768px breakpoint, use shadcn-nuxt Card components with brutal styling, stack information vertically, and apply Neo-Brutalism spacing system with gap-brutal utilities for consistent mobile UX.
- For DeFi agent animations: use @vueuse/motion as primary animation library with sharp/mechanical animations for Neo-Brutalism aesthetic, implement v-motion directives (slide-left/right, pop, fade, bounce) with 200-300ms timing, focus on GPU-accelerated properties, respect prefers-reduced-motion, and always consider motion when creating new components.

# Git Workflow
- User prefers organizing git commits into logical groups with descriptive conventional commit format messages (e.g., 'feat: add enhanced Neo-Brutalism color palette') and wants changes pushed to main branch after committing.

# Web3 Integration
- User prefers Wagmi v2 + Viem stack for Web3/EVM wallet integration with Bun compatibility, using bunx for package management, and implementing SIWE authentication with EIP-712 signing for DeFi applications.
- User prefers SIWE (Sign-In with Ethereum) authentication with EIP-712 signing for Web3 login flows, distinguishing between wallet connected and user authenticated states, with persistent authentication sessions and proper logout functionality that clears both wallet connection and authentication state.
- For DeFi agent Web3 integration: use Wagmi v2 + Viem with Bun package manager, implement SIWE authentication with EIP-712 signing, focus on Ethereum single-chain MVP scope, ensure Nuxt 3 compatibility with shadcn-nuxt and Neo-Brutalism design patterns.